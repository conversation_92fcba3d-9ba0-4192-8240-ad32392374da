import React from "react";
import bag1 from "../assets/bag1.jpg";
const HomePage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-pink-900">
      {/* Hero Section */}
      <div className="container mx-auto px-8 py-20">
        <div className="flex items-center justify-between min-h-[80vh]">
          {/* Left Content */}
          <div className="flex-1 pr-12">
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold text-white leading-tight mb-8">
              TÚI XA XỈ CHO <span className="block">PHÁI ĐẸP</span>
            </h1>

            <p className="text-lg md:text-xl text-gray-300 mb-8 max-w-lg leading-relaxed">
              KHÁM PHÁ BỘ SƯU TẬP TÚI XÁCH CAO CẤP TỪ CÁC THƯƠNG HIỆU HÀNG ĐẦU
              THẾ GIỚI, MANG ĐẾN PHONG CÁCH VÀ ĐẲNG CẤP CHO MỌI QUÝ CÔ.
            </p>

            <button className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105 flex items-center gap-2">
              Mua Ngay
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>

          {/* Right Content - Image */}
          <div className="flex-1 flex justify-center items-center">
            <div className="relative">
              {/* Placeholder for bag image - you can replace this with actual image */}
              <div className="w-96 h-96 bg-gradient-to-br from-gray-200 to-gray-400 rounded-lg shadow-2xl flex items-center justify-center">
                <div className="text-center text-gray-600">
                  <img src={bag1} alt="Bag" />
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-pink-500 rounded-full opacity-70"></div>
              <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-purple-500 rounded-full opacity-50"></div>
              <div className="absolute top-1/2 -right-8 w-6 h-6 bg-white rounded-full opacity-60"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
